// import { Avatar, Dropdown, Image, Form, Input, Layout, Menu, Modal, notification, Spin, Row, Col, Button, Space } from "antd";
// import { useState, useEffect } from "react"
// import logo from './../logo-lg.png';
// import Login from "./Login"
// import {
//     useHistory,
//     Switch,
//     Route,
// } from "react-router-dom"
// import apiClient from "../config/apiClient";
// import { logout } from "../config/routes";
// import ErrorBoundary from "./ErrorBoundary";
// import DialerPage from "./DialerPage";
// import { QuestionCircleOutlined, UserSwitchOutlined } from "@ant-design/icons";
// import '../App.css'

// const openNotificationWithIcon = (type, message) => {
//     notification[type]({
//         message: type === 'success' ? 'Success' : 'Error',
//         description: message
//     });
// };

// function Main() {

// const [loggedin, setLoggedin] = useState(sessionStorage.getItem('loggedin') === "true")
// const [username, setUsername] = useState(sessionStorage.getItem('agent_username'))
// const [error,] = useState(false)
// const [loading, setLoading] = useState(false)
// const [passwordModal, setPasswordModal] = useState(false)
// let [form] = Form.useForm()

//     let history = useHistory()

//     useEffect(() => {
//         if (loggedin) {
//             history.push('/dialer')
//         } else {
//             history.push('/')
//         }
//     }, [loggedin])

//     useEffect(() => {
//         if (!sessionStorage.getItem('agent_username')) {
//             console.log('User Logged Out');
//         }
//     }, [username])

//     const handleLogout = () => {
//         setLoading(true)
//         // Fixed logout issue for expired sessions (BUG)
//         apiClient.post(logout)
//             .then(_ => {
//                 setLoading(false)
//                 setLoggedin(false)
//                 sessionStorage.clear()
//                 localStorage.clear()
//                 setUsername(null)
//             })
//             .catch(e => {
//                 setLoading(false)
//                 if (e.response) {
//                     if (e.response.status === 401) {
//                         sessionStorage.clear()
//                         window.location.reload()

//                     }
//                 }
//             })
//     }
//     useEffect(() => {
//         if (error) {
//             openNotificationWithIcon('error', error)
//         }
//     }, [error])

//     const handlePasswordModal = () => {
//         setPasswordModal(!passwordModal)
//     }

//     const onChangePassword = (values) => {
//         apiClient.post('/api/changePassword', values).then((res) => {
//             openNotificationWithIcon('success', res.data)
//             form.resetFields()
//             setPasswordModal(!passwordModal)
//             handleLogout();
//         }).catch(err => {
//             openNotificationWithIcon('error', err.response?.data?.message)
//             form.resetFields()
//             setPasswordModal(!passwordModal)
//         })
//     }


// const menu = (
//     <Menu>
//         <Menu.Item key={1} onClick={handlePasswordModal}>Change Password</Menu.Item>
//         <Menu.Item key={2} onClick={handleLogout}>Logout</Menu.Item>
//     </Menu>
// );

//     return (
//         <>
//             <Image preview={false} src={logo} height={60} alt="Contact+ Logo" style={{ position: 'absolute', top: 0 }} />
//             <Spin spinning={loading}>
//                 <Layout style={{ minHeight: "100vh" }} className="bg-image">
//                     {1 === 3 && <Layout.Header style={{ backgroundColor: !username ? '#e5e6e8' : '#fff', display: 'none' }}>
// <Row justify="space-between">
//     <Col>
//         <Image preview={false} src={logo} height={60} alt="Contact+ Logo" />
//     </Col>
//     <Col>
//         <Space>
//             <Button
//                 shape="circle"
//                 title="Help"
//                 icon={<QuestionCircleOutlined />}
//                 style={{ border: 'none' }}
//                 href="https://manuals.telecard.com.pk/agent"
//                 target="_blank"
//             />
//             {username && <Dropdown trigger={['click']} overlay={menu}>
//                 <Button
//                     icon={<UserSwitchOutlined />}
//                     size="large"
//                     style={{
//                         border: 'none',
//                         boxShadow: 'none',
//                     }}
//                 >
//                     {username || ''}
//                 </Button>
//             </Dropdown>}
//         </Space>
//     </Col>
// </Row>
// {/* <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
//     <Image preview={false} src={logo} alt="Contact+ Logo" />
//     {loggedin && <Dropdown trigger={['click']} overlay={menu}>
//         <Avatar
//             size={40}
//             style={{
//                 background: '#02AA31',
//                 cursor: 'pointer',
//                 textTransform:'capitalize',
//                 fontWeight:'bold'
//             }}
//         >
//             {sessionStorage.getItem('agent_username')[0] || ''}
//         </Avatar>
//     </Dropdown>}
// </div> */}
//                     </Layout.Header>}
//                     <Layout.Content className={!username ? "bg-image" : 'bg-none'}>
// <div className="site-layout-contednt">
//     <Switch>
//         <Route path="/dialer">
//             <ErrorBoundary>
//                 <DialerPage handleLogout={handleLogout} />
//             </ErrorBoundary>
//         </Route>
//         <Route path="/">
//             <Login setLoggedin={setLoggedin} setUsername={setUsername} />
//         </Route>
//     </Switch>
// </div>
//                     </Layout.Content>
//                     {username && <Layout.Footer style={{ textAlign: 'center', backgroundColor: '#FFF', fontWeight: 'bold' }}>ContactPlus ©{new Date().getFullYear()} | Powered by Telecard Ltd.</Layout.Footer>}
//                 </Layout>
//             </Spin>
//             <ChangePasswordModal
//                 isModalOpen={passwordModal}
//                 handleOk={onChangePassword}
//                 form={form}
//                 onCancel={() => {
//                     setPasswordModal(!passwordModal)
//                     form.resetFields()
//                 }}
//             />
//         </>
//     )
// }

// const ChangePasswordModal = ({ isModalOpen, handleOk, onCancel, form }) => {
//     return (
//         <Modal
//             title="Change Password"
//             visible={isModalOpen}
//             onOk={() => {
//                 form.validateFields().then(values => {
//                     handleOk(values)
//                 })
//             }}
//             onCancel={onCancel}
//         >
//             <Form
//                 size="large"
//                 form={form}
//             >
//                 <Form.Item
//                     name="current_password"
//                     rules={[
//                         {
//                             required: true,
//                             message: 'Please input your password!',
//                         },
//                     ]}
//                 >
//                     <Input.Password placeholder="Enter Current Password." />
//                 </Form.Item>

//                 <Form.Item
//                     name="new_password"
//                     rules={[
//                         {
//                             required: true,
//                             message: 'Please input your password!',
//                         },
//                     ]}
//                     hasFeedback
//                 >
//                     <Input.Password placeholder="Enter New Password." />
//                 </Form.Item>

//                 <Form.Item
//                     name="new_confirm_password"
//                     dependencies={['new_password']}
//                     hasFeedback
//                     rules={[
//                         {
//                             required: true,
//                             message: 'Please confirm your password!',
//                         },
//                         ({ getFieldValue }) => ({
//                             validator(_, value) {
//                                 if (!value || getFieldValue('new_password') === value) {
//                                     return Promise.resolve();
//                                 }
//                                 return Promise.reject(new Error('The two passwords that you entered do not match!'));
//                             },
//                         }),
//                     ]}
//                 >
//                     <Input.Password placeholder="Enter Confirm Password." />
//                 </Form.Item>
//             </Form>
//         </Modal>
//     )
// }

// export default Main

import { Avatar, Dropdown, Image, Form, Input, Layout, Menu, Modal, notification, Spin, Row, Col, Button, Space } from "antd";
import { useState, useEffect } from "react"
import logo from './../logo-lg.png';
import Login from "./Login"
import {
    useHistory,
    Switch,
    Route,
} from "react-router-dom"
import apiClient from "../config/apiClient";
import { logout } from "../config/routes";
import ErrorBoundary from "./ErrorBoundary";
import DialerPage from "./DialerPage";
import { QuestionCircleOutlined, UserSwitchOutlined } from "@ant-design/icons";
import DialerWrapper from "./DialerWrapper";

const openNotificationWithIcon = (type, message) => {
    notification[type]({
        message: type === 'success' ? 'Success' : 'Error',
        description: message
    });
};
const { Header, Footer, Sider, Content } = Layout;

function Main() {
    const [loggedin, setLoggedin] = useState(sessionStorage.getItem('loggedin') === "true")
    const [username, setUsername] = useState(sessionStorage.getItem('agent_username'))
    const [error,] = useState(false)
    const [loading, setLoading] = useState(false)
    const [passwordModal, setPasswordModal] = useState(false)
    let [form] = Form.useForm()

    const handleLogout = () => {
        setLoading(true)
        // Fixed logout issue for expired sessions (BUG)
        apiClient.post(logout)
            .then(_ => {
                setLoading(false)
                setLoggedin(false)
                sessionStorage.clear()
                localStorage.clear()
                setUsername(null)
            })
            .catch(e => {
                setLoading(false)
                if (e.response) {
                    if (e.response.status === 401) {
                        sessionStorage.clear()
                        window.location.reload()

                    }
                }
            })
    }
    useEffect(() => {
        if (error) {
            openNotificationWithIcon('error', error)
        }
    }, [error])

    const handlePasswordModal = () => {
        setPasswordModal(!passwordModal)
    }

    const onChangePassword = (values) => {
        apiClient.post('/api/changePassword', values).then((res) => {
            openNotificationWithIcon('success', res.data)
            form.resetFields()
            setPasswordModal(!passwordModal)
            handleLogout();
        }).catch(err => {
            openNotificationWithIcon('error', err.response?.data?.message)
            form.resetFields()
            setPasswordModal(!passwordModal)
        })
    }

    const menu = (
        <Menu>
            <Menu.Item key={1} onClick={handlePasswordModal}>Change Password</Menu.Item>
            <Menu.Item key={2} onClick={handleLogout}>Logout</Menu.Item>
        </Menu>
    );

    return (
        <Switch>
            <Route path="/dialer">
                <ErrorBoundary>
                    <DialerPage handleLogout={handleLogout} />
                </ErrorBoundary>
            </Route>
            <Route path="/">
                <Login setLoggedin={setLoggedin} setUsername={setUsername} />
            </Route>
        </Switch>
    )
}


export default Main