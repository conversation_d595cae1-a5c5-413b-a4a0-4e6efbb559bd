import React, { useEffect, useState } from "react"
import { useQuery } from "react-query";
import { fetchDomain, fetchQueue, fetchUser } from "../config/queries";
import { Button, Result, Skeleton } from "antd";
import { LogoutOutlined } from "@ant-design/icons";
import DialerLayout from "../dialer/DialerLayout";
import { useMemo } from "react";

const DialerPage = ({ handleLogout }) => {

    const options = {
        refetchInterval: false,
        refetchOnMount: true,
        refetchOnWindowFocus: true,
        retry: false
    }
    const domainQuery = useQuery('fetchDomain', fetchDomain, options)
    const userQuery = useQuery('fetchUser', fetchUser, options)
    const queueQuery = useQuery('fetchQueue', fetchQueue, options)
    const [data, setData] = useState({
        user: {

        },
        domain: {},
        queue: {}
    })
    const [loading, setLoading] = useState(true)
    const [errorFlag, setErrorFlag] = useState(false)

    useEffect(() => {
        fetchAllData()
            .then(r => setData(r))
            .catch(e => setErrorFlag(true))
            .finally(() => setLoading(false))
    }, [])


    const fetchAllData = async () => ({
        'domain': await fetchDomain(),
        'user': await fetchUser(),
        'queue': await fetchQueue()
    })

    useMemo(() => {
        sessionStorage.setItem('auth_username', data.user.auth_username)
    }, [data.user.auth_username])

    return (
        <Skeleton loading={loading}>
            {data && data.user.type === 'Normal' ? <>
                <Result
                    status="error"
                    title="401"
                    subTitle="Sorry, you are unauthorized to login into this application. Please contact your system administrator."
                    extra={<Button onClick={handleLogout} icon={<LogoutOutlined />} type="primary">Logout</Button>}
                />
            </> : errorFlag ? <>
                <Result
                    status={500}
                    title={500}
                    subTitle={"Error in fetching parameters from server. Please contact your system administrator."}
                    extra={<Button onClick={() => window.location.reload()} type="primary">Refresh Page</Button>}
                />
            </> : <>
                <DialerLayout
                    user={data.user}
                    name={data.user.name}
                    sipDomain={data.domain.server_address}
                    authUser={data.user.auth_username}
                    authPass={data.user.auth_password}
                    wssPort={data.domain.wss_port}
                    queues={data.queue}
                    settings={data.domain}
                    hangupEnable={data.user?.hangup_enable}
                />
            </>}
        </Skeleton>
    )

    /*if(userQuery.isSuccess && userQuery.data.type === "Normal") {
        return (
            <Result
                status="error"
                title="401"
                subTitle="Sorry, you are unauthorized to login into this application. Please contact your system administrator."
                extra={<Button icon={<LogoutOutlined />} type="primary">Logout</Button>}
            />
        )
    } else if (userQuery.isError || domainQuery.isError) {
        return (
          <Result
            status={userQuery.error.status ?? 500}
            title={userQuery.error.status ?? 500}
            subTitle={userQuery.error.statusText ?? error}
            extra={<Button onClick={() => window.location.reload()} type="primary">Refresh Page</Button>}
          />
        )
    } else if(domainQuery.isLoading || userQuery.isLoading) {
        return (
            <Row justify="center">
                <Col span={20}>
                    <Skeleton paragraph={{ rows: 10 }} active />
                </Col>
            </Row>
        )
    } else return (
        <Spin spinning={domainQuery.isLoading || userQuery.isLoading}>
            <DialerLayout
                name={userQuery.data.name}
                sipDomain={domainQuery.data.server_address}
                authUser={userQuery.data.auth_username}
                authPass={userQuery.data.auth_password}
                wssPort={domainQuery.data.wss_port}
                queues={queueQuery.data}
                settings={domainQuery.data}
            />
        </Spin>
    )*/
}

export default DialerPage