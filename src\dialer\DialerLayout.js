import {
    But<PERSON>,
    Card,
    Col,
    Descriptions,
    Dropdown,
    Form,
    Image,
    Input,
    Layout,
    Menu,
    Modal,
    notification,
    Row,
    Space,
    Spin,
} from "antd";
import React, {
    useState,
    useRef,
    useEffect,
    useContext,
    useLayoutEffect,
} from "react";
import {
    BoxPlotOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    ClusterOutlined,
    CodeOutlined,
    ContainerOutlined,
    DeliveredProcedureOutlined,
    EyeInvisibleOutlined,
    FormatPainterOutlined,
    FormOutlined,
    LoginOutlined,
    LogoutOutlined,
    RedoOutlined,
    SearchOutlined,
    SettingOutlined,
    SnippetsOutlined,
    StopOutlined,
} from "@ant-design/icons";
import DialerAccount from "../dialer/DialerAccount";
import SIPModule from "./SIPModule";
import { useMutation, useQuery, useQueryClient } from "react-query";
import {
    fetchACDRs,
    fetchAgentStatusInQueue,
    fetchAStats,
    fetchChannelId,
    fetchIsReady,
    fetchLast5Calls,
    fetchLoginQueue,
    fetchLogoutQueue,
    fetchNotReadyQueue,
    fetchPauseReasons,
    fetchQStats,
    fetchReadyQueue,
    fetchWorkcodes,
} from "../config/queries";
import openSuccessNotificationWithIcon from "../components/Message";
import openNotificationWithIcon from "../components/Notification";
import Widget, { WidgetScript } from "../components/Widget";
import CallDetailWidget from "../components/CallDetailWidget";
import { useStorageState } from "react-storage-hooks";
import Workcode from "../components/Workcode";
import { postNotReady, postWorkcode, postUnPause } from "../config/mutations";
import NotReady from "../components/NotReady";
import AgentStatusWidget from "../components/AgentStatusWidget";
import CampaignWidget from "../components/CampaignWidget";
import { FormWidget } from "../components/FormWidget";
import { useDispatch, useSelector } from "react-redux";
import { getCallId } from "../Actions/CallAction";
import { WorkcodeWidget } from "../components/WorkcodeWidget";
import { getOutgoingCallId } from "../Actions/OutgoingCallAction";
import Last5CallHistory from "../components/Last5CallHistory";
import { fetchAgentScript } from "../Actions/ScriptActions";
import apiClient from "../config/apiClient";
import { handleError } from "../Shared/handleError";
import axios from "axios";
import { timerFunc } from "../Shared/timerFunc";
import { logout } from "../config/routes";
import { QuestionCircleOutlined, UserSwitchOutlined } from "@ant-design/icons";
import logo from './../logo-lg.png';
import { useHistory } from "react-router-dom";
import CedarComponent from "../components/CedarComponent";


export default function DialerLayout(props) {
    const queryClient = useQueryClient();
    const history = useHistory()

    const [queueStats, setQueueStats, writeErrorQueue] = useStorageState(
        localStorage,
        "queue-stats",
        false
    );
    const [agentStats, setAgentStats, writeErrorAgent] = useStorageState(
        localStorage,
        "agent-stats",
        false
    );
    const [cdrStats, setCdrStats, writeErrorCdr] = useStorageState(
        localStorage,
        "cdr-stats",
        false
    );
    const [lastCallStats, setLastCallStats, writeErrorLastCall] = useStorageState(
        localStorage,
        "last-calls-stats",
        false
    );
    const [aaStats, setAaStats, writeErrorAa] = useStorageState(
        localStorage,
        "aa-stats",
        false
    );
    const [refetchInterval, setRefetchInterval, writeErrorRefetchInterval] =
        useStorageState(localStorage, "refetch-interval", 5);

    const formState = useSelector((state) => state.FormWidgetReducer);

    // State vars
    const [callHistory, setCallHistory] = useState([]);
    const [number, setNumber] = useState();
    const [connected, setConnected] = useState(false);
    const [callHangup, setCallHangup] = useState(false);
    const [channelId, setChannelId] = useState(false);
    const [notReadyVisible, setNotReadyVisible] = useState(false);
    const [notReadyReason, setNotReadyReason] = useState(false);
    const [showCampaign, setShowCampaign] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isReady, setIsReady] = useState(false);
    const [readyButtonDisable, setReadyButtonDisable] = useState(false);
    const [campaignStatus, setCampaignStatus] = useState(false);
    const [campaignPauseStatus, setCampaignPauseStatus] = useState(false);
    const [activeCampaign, setActiveCampaign] = useState(false);
    const [isCallAccepted, setIsCallAccepted] = useState(false);
    const [cdrPage, setCdrPage] = useState(false);
    const [filteredNumber, setFilteredNumber] = useState();
    //   const [isCallAccepted, setIsCallAccepted] = useState(false);

    //   const [formWidgetDataOnCallAcceptance, setFormWidgetDataOnCallAcceptance] =
    //     useState();

    const queueState = useSelector((state) => state.QueueReducer);
    const scriptState = useSelector((state) => state.ScriptReducer);
    const callState = useSelector((state) => state.CallReducer);

    const [queueLoggedIn, setQueueLoggedIn] = useState(false);
    const [onBreak, setOnBreak] = useState(false);
    const [timer, setTimer] = useState(0);
    const [breakTimer, setBreakTimer] = useState(0);
    const [loggedin, setLoggedin] = useState(sessionStorage.getItem('loggedin') === "true")
    const [username, setUsername] = useState(sessionStorage.getItem('agent_username'))
    const [error,] = useState(false)
    const [loading, setLoading] = useState(false)
    const [passwordModal, setPasswordModal] = useState(false)
    let [form] = Form.useForm()
    // Form state
    const [forms, setForms] = useState([]);
    const [selectedFormfromMenu, setSelectedFormfromMenu] = useState();
    const [formVisible, setFormVisible] = useState(false);
    const [outgoingDialed, setOutgoingDialed] = useState(false)

    // Workcode State
    const [workcodeVisible, setWorkcodeVisible] = useState(false);

    // Incoming call state
    const [incomingCallAccepted, setIncomingCallAccepted] = useState(false);
    const dispatch = useDispatch();

    // Outgoing Call State
    const [outgoingCallAccepted, setOutgoingCallAccepted] = useState(false);

    // cedar const
    const [fetchLoading, setFetchLoading] = useState(false)
    const [cedarVisible, setCedarVisible] = useState(false)
    const [cedarFetchResult, setCedarFetchResult] = useState([])

    useEffect(() => {
        if (incomingCallAccepted) dispatch(getCallId());
    }, [incomingCallAccepted]);

    useEffect(() => {
        if (formState.forms) setForms(formState.forms);
    }, [formState.forms, forms]);

    //   useEffect(() => {
    //     console.log("object bb number", number);
    //   }, [number]);

    // checking for agent logged in
    useEffect(() => {
        let interval = setInterval(() => {
            apiClient
                .post("/api/agent/is-login")
                .then((res) => setQueueLoggedIn(res.data));
        }, 5000);




        return () => {
            clearInterval(interval);
        };



    }, []);

    const settingsProps = {
        queueStats,
        setQueueStats,
        agentStats,
        setAgentStats,
        cdrStats,
        setCdrStats,
        lastCallStats,
        setLastCallStats,
        aaStats,
        setAaStats,
        refetchInterval,
        setRefetchInterval,
    };

    const incomingCallProps = {
        incomingCallAccepted,
        setIncomingCallAccepted,
    };

    const outgoingCallProps = {
        outgoingCallAccepted,
        setOutgoingCallAccepted,
    };

    useEffect(() => {
        if (outgoingDialed) dispatch(getOutgoingCallId());
    }, [outgoingDialed]);

    useEffect(() => {
        if (queueState.queues && queueState.queues.length > 0)
            dispatch(fetchAgentScript(queueState.queues));
    }, [queueState.queues]);

    useEffect(() => {
        fetchLast5Calls(number)
            .then((r) => setCallHistory(r.data))
            .catch((e) => console.log(e));
    }, [number]);

    // Mutations
    const notReadyMutation = useMutation(postNotReady, {
        onSuccess: (r, variables) => {
            setNotReadyVisible(false);
            setIsLoading(false);
            setReadyButtonDisable(false);
            queryClient
                .invalidateQueries("isReadyQuery")
                .catch((e) => console.log(e));
            queryClient.invalidateQueries("aStats").catch((e) => console.log(e));
            switch (r.data.response) {
                case "Success":
                    openSuccessNotificationWithIcon(r.data.message);
                    break;
                case "Error":
                    openNotificationWithIcon(r.data.message);
                    break;
                default:
                    break;
            }
        },
        onError: (error, variables) =>
            openNotificationWithIcon(error?.response?.data),
    });

    const unPauseMutation = useMutation(postUnPause, {
        onSuccess: (r, variables) => {
            setIsReady(false);
            queryClient
                .invalidateQueries("isReadyQuery")
                .catch((e) => console.log(e));
            switch (r.response) {
                case "Success":
                    openSuccessNotificationWithIcon(r.message);
                    break;
                case "Error":
                    openNotificationWithIcon(r.message);
                    break;
                default:
                    break;
            }
        },
        onError: (error, variables) => {
            openNotificationWithIcon(error?.response?.data);
            setIsReady(false);
        },
    });

    const workcodeMutation = useMutation(postWorkcode, {
        onSuccess: (response) => {
            openSuccessNotificationWithIcon(response.data);
            setChannelId(null);
        },
        onError: (error) => openNotificationWithIcon(error.response.data.message),
    });

    const options = {
        refetchInterval: 5000,
        refetchOnReconnect: true,
        refetchOnMount: true,
        refetchOnWindowFocus: true,
    };

    const qStatsQuery = useQuery("qStats", fetchQStats, {
        refetchOnReconnect: false,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        enabled: queueStats,
        refetchInterval: parseInt(refetchInterval) * 1000,
    });
    const aStatsQuery = useQuery("aStats", fetchAStats, {
        refetchOnReconnect: false,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        enabled: agentStats,
        refetchInterval: parseInt(refetchInterval) * 1000,
    });
    const aCDRQuery = useQuery(["aCDR", cdrPage, filteredNumber], fetchACDRs, {
        refetchOnReconnect: false,
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        enabled: cdrStats,
        refetchInterval: parseInt(refetchInterval) * 1000,
    });

    // const agentScript = useQuery('agentScript', fetchAgentsScript, options)
    const workcodeQuery = useQuery("workCode", fetchWorkcodes, options);
    const pauseReasonQuery = useQuery("pauseReason", fetchPauseReasons, options);
    const agentStatusInQueue = useQuery(
        "agentStatusInQueue",
        fetchAgentStatusInQueue,
        {
            ...options,
            enabled: queueLoggedIn,
        }
    );

    const getChannelIdQuery = useQuery("getChannelId", fetchChannelId, {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        enabled: false,
    });

    const loginQueueQuery = useQuery("loginQueue", fetchLoginQueue, {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        enabled: false,
    });
    const logoutQueueQuery = useQuery("logoutQueue", fetchLogoutQueue, {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        enabled: false,
    });
    const readyQueueQuery = useQuery("readyQueue", fetchReadyQueue, {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        enabled: false,
    });
    const notReadyQueueQuery = useQuery("notReadyQueue", fetchNotReadyQueue, {
        retry: false,
        refetchOnMount: false,
        refetchOnReconnect: false,
        refetchOnWindowFocus: false,
        enabled: false,
    });
    const isReadyQuery = useQuery("isReadyQuery", fetchIsReady, options);
    const [dialerVisible, setDialerVisible] = useState(false);
    const [dialerAccountVisible, setDialerAccountVisible] = useState(false);
    let sipModule = null;

    useEffect(() => {
        if (campaignStatus) {
            logoutQueue();
        }
    }, [campaignStatus]);

    useEffect(() => {
        if (
            (outgoingDialed || incomingCallAccepted) &&
            forms.some((record) => record.default === 1)
        ) {
            setFormVisible(true);
        }
    }, [outgoingDialed, incomingCallAccepted, forms]);

    useEffect(() => {
        console.log("Not ready", isReadyQuery?.data)
        if (isReadyQuery?.data) {
            setReadyButtonDisable(true)
        }
    }, [isReadyQuery?.data])
    // isReadyQuery.data?.length > 0
    // ? !isReadyQuery.data[0]
    // : !isReadyQuery.data

    useEffect(() => {
        // find the record with default set to 1
        const recordWithDefault = forms.find((record) => record.default === 1);
        // get the id of the record with default set to 1
        const id = recordWithDefault ? recordWithDefault.id : null;
        if (outgoingDialed || incomingCallAccepted && number) {
            apiClient
                .get(`api/getFormData?phone_number=${number}&form_id=${id}`)
                .then((res) => {
                    setSelectedFormfromMenu(res.data);
                });
        } else if (formVisible && !number) {
            apiClient.get(`api/getFormData?form_id=${id}`).then((res) => {
                setSelectedFormfromMenu(res.data);
            });
        }
    }, [outgoingDialed, incomingCallAccepted]);

    const alertUser = (e) => {
        if (agentStatusInQueue.data) {
            logoutQueueQuery.refetch();
        }
    };

    const showDialerDrawer = () => {
        setDialerVisible(true);
    };

    const onDialerClose = () => {
        setDialerVisible(false);
    };

    const onDialerAccountClose = () => {
        setDialerAccountVisible(false);
    };

    const showDialerAccount = () => {
        setDialerAccountVisible(true);
    };

    const registerSIP = () => {
        sipModule.RegisterSIP();
    };

    const unregisterSIP = () => {
        sipModule.UnregisterSIP();
    };

    const refreshQStats = () => {
        qStatsQuery.refetch();
    };

    const refreshAStats = () => {
        aStatsQuery.refetch();
    };

    function setZeros(i) {
        if (i < 10) return "0" + i;
        return i;
    }

    /**
     * Login to queue function
     */
    const loginQueue = () => {
        // Login queue
        loginQueueQuery
            .refetch()
            .then((r) => {
                if (r.isError) {
                    openNotificationWithIcon(r.error?.response?.data);
                } else {
                    switch (r.data.response) {
                        case "Success":
                            openSuccessNotificationWithIcon(r.data.message);
                            fetchTime();
                            break;
                        case "Error":
                            openNotificationWithIcon(r.data.message);
                            break;
                        default:
                            break;
                    }
                }
            })
            .then(() => queryClient.invalidateQueries("agentStatusInQueue"))
            .catch((e) => console.log(e));
    };

    /**
     * Logout from queue
     */
    const logoutQueue = () => {
        // Logout queue
        logoutQueueQuery
            .refetch()
            .then((r) => {
                if (r.isError) {
                    openNotificationWithIcon(r.error?.response?.data);
                } else {
                    switch (r.data.response) {
                        case "Success":
                            openSuccessNotificationWithIcon(r.data.message);
                            fetchTime();
                            break;
                        case "Error":
                            openNotificationWithIcon(r.data.message);
                            break;
                        default:
                            break;
                    }
                }
            })
            .then(() => queryClient.invalidateQueries("agentStatusInQueue"))
            .catch((e) => console.log(e));
    };

    /**
     * Unpause agent
     */
    const readyAgent = () => {
        // Ready agent
        setIsReady(true);
        setReadyButtonDisable(true);
        unPauseMutation.mutate({ reason: notReadyReason });
    };

    /**
     * Pause agent
     */
    const notReadyAgent = () => {
        setNotReadyVisible(true);
    };

    const submitNotReady = () => {
        notReadyMutation.mutate({ reason: notReadyReason });
        setIsLoading(true);
        // Not Ready agent
        /*notReadyMutation.mutate   ({ reason: notReadyReason }).then(r => {
                switch (r.data.response) {
                    case "Success":
                        openSuccessNotificationWithIcon(r.data.message)
                        break
                    case "Error":
                        openNotificationWithIcon(r.data.message)
                        break
                    default:
                        break
                }
            }).then(() => queryClient.invalidateQueries('isReadyQuery')).catch(e => console.log(e))*/
    };

    const submitWorkcode = (workcode) => {
        workcodeMutation.mutate({ code: workcode, channel: channelId });
    };

    const formProps = {
        formVisible,
        setFormVisible,
        isCallAccepted,
        setIsCallAccepted,
        number,
        selectedFormfromMenu,
        setOutgoingDialed
    };

    const workcodeProps = {
        workcodeVisible,
        setWorkcodeVisible,
    };

    const campaignProps = {
        showCampaign,
        setShowCampaign,
        activeCampaign,
        setActiveCampaign,
        user: props.user,
    };

    const campaignPropsForSip = {
        campaignStatus,
        activeCampaign,
    };

    useEffect(() => {
        if (!isReadyQuery.data) setOnBreak(true);
        else setOnBreak(false);
    }, [isReadyQuery.data]);

    const fetchTime = async () => {
        try {
            const result = await apiClient.post("/api/agent/getTime");
            if (result.data) {
                setTimer(result.data.loginTime);
                setBreakTimer(result.data.breakTime);
            }
        } catch (error) {
            console.log("err", error);
        }
    };

    useLayoutEffect(() => {
        fetchTime();
    }, []);


    // fetch data cedar

    const getFetchData = (number) => {
        if (number) {
            setFetchLoading(true)
            apiClient.post(`/api/cedar/getStudentData?phone_number=${number}`).then((res) => {
                if (res.data) {
                    setFetchLoading(false)
                    setCedarFetchResult(res.data.result)
                }
            })
        }
    }

    useEffect(() => {
        if (cedarVisible) getFetchData(number)
    }, [cedarVisible, number])

    const handleLogout = () => {
        setLoading(true)
        // Fixed logout issue for expired sessions (BUG)
        apiClient.post(logout)
            .then(_ => {
                setLoading(false)
                setLoggedin(false)
                setUsername(null)
                sessionStorage.clear()
                localStorage.clear()
                history.push('/')

            })
            .catch(e => {
                setLoading(false)
                if (e.response) {
                    if (e.response.status === 401) {
                        sessionStorage.clear()
                        window.location.reload()

                    }
                }
            })
    }
    useEffect(() => {
        if (error) {
            openNotificationWithIcon('error', error)
        }
    }, [error])

    const handlePasswordModal = () => {
        setPasswordModal(!passwordModal)
    }

    const onChangePassword = (values) => {
        apiClient.post('/api/changePassword', values).then((res) => {
            openNotificationWithIcon('success', res.data)
            form.resetFields()
            setPasswordModal(!passwordModal)
            handleLogout();
        }).catch(err => {
            openNotificationWithIcon('error', err.response?.data?.message)
            form.resetFields()
            setPasswordModal(!passwordModal)
        })
    }

    const menu = (
        <Menu>
            <Menu.Item key={1} onClick={handlePasswordModal}>Change Password</Menu.Item>
            <Menu.Item key={2} onClick={handleLogout}>Logout</Menu.Item>
        </Menu>
    );
    return (
        <Layout style={{ minHeight: '100vh' }}>
            <Layout.Header style={{ background: '#fff' }}>
                <Row justify="space-between">
                    <Col>
                        <Image preview={false} src={logo} height={60} alt="Contact+ Logo" />
                    </Col>
                    <Col>
                        <Space>
                            <Button
                                shape="circle"
                                title="Help"
                                icon={<QuestionCircleOutlined />}
                                style={{ border: 'none' }}
                                href="https://manuals.telecard.com.pk/agent"
                                target="_blank"
                            />
                            {username && <Dropdown trigger={['click']} overlay={menu}>
                                <Button
                                    icon={<UserSwitchOutlined />}
                                    size="large"
                                    style={{
                                        border: 'none',
                                        boxShadow: 'none',
                                    }}
                                >
                                    {username || ''}
                                </Button>
                            </Dropdown>}
                        </Space>
                    </Col>
                </Row>
            </Layout.Header>
            <Layout>
                <Layout.Sider theme="light" collapsed>
                    <Menu
                        defaultSelectedKeys={["1"]}
                        mode="inline"
                        theme="light"
                        inlineCollapsed={true}
                        style={{ minHeight: "79vh" }}
                    >
                        <Menu.Item
                            onClick={showDialerDrawer}
                            key="1"
                            icon={<ContainerOutlined />}
                        >
                            Dialer
                        </Menu.Item>
                        <Menu.Item
                            onClick={showDialerAccount}
                            key="2"
                            icon={<SettingOutlined />}
                        >
                            Settings
                        </Menu.Item>
                        <Menu.Item onClick={registerSIP} key="register" icon={<CodeOutlined />}>
                            Register
                        </Menu.Item>
                        <Menu.Item onClick={unregisterSIP} key="unregister" icon={<StopOutlined />}>
                            Unregister
                        </Menu.Item>
                        <Menu.Item onClick={loginQueue} key="queue_login" icon={<ClusterOutlined />}>
                            Queue Login
                        </Menu.Item>
                        <Menu.Item
                            onClick={logoutQueue}
                            key="queue_logout"
                            icon={<DeliveredProcedureOutlined />}
                        >
                            Queue Logout
                        </Menu.Item>
                        <Menu.Item
                            disabled={readyButtonDisable}
                            onClick={readyAgent}
                            key="ready_agent"
                            icon={
                                <Spin spinning={isReady}>
                                    <CheckCircleOutlined />
                                </Spin>
                                // <CheckCircleOutlined />
                            }
                        >
                            Ready
                        </Menu.Item>
                        <Menu.Item
                            disabled={
                                isReadyQuery.data?.length > 0
                                    ? !isReadyQuery.data[0]
                                    : !isReadyQuery.data
                            }
                            onClick={notReadyAgent}
                            key="not_ready"
                            icon={<CloseCircleOutlined />}
                        >
                            Not Ready
                        </Menu.Item>
                        <Menu.Item
                            danger={incomingCallAccepted || outgoingCallAccepted}
                            onClick={() => setWorkcodeVisible(true)}
                            key="workcode"
                            icon={<FormOutlined />}
                        >
                            Workcode
                        </Menu.Item>
                        {forms.length > 0 &&
                            forms.map((value, index) => (
                                <Menu.Item
                                    // danger={incomingCallAccepted || outgoingCallAccepted}
                                    onClick={(e) => {
                                        setFormVisible(true);
                                        number
                                            ? apiClient
                                                .get(
                                                    `api/getFormData?phone_number=${number}&form_id=${value.id}`
                                                )
                                                .then((res) => {
                                                    setSelectedFormfromMenu(res.data);
                                                })
                                            : apiClient
                                                .get(`api/getFormData?form_id=${value.id}`)
                                                .then((res) => {
                                                    setSelectedFormfromMenu(res.data);
                                                });
                                    }}
                                    key={value.id}
                                    icon={<SnippetsOutlined />}
                                >
                                    {value.name}
                                </Menu.Item>
                            ))}


                        <Menu.Item
                            disabled={!number}
                            // disabled={number.length > 0 ? false : true}
                            onClick={() => setCedarVisible(!cedarVisible)}
                            key="fetchData"
                            icon={<RedoOutlined />}
                        >
                            Fetch Data
                        </Menu.Item>
                    </Menu>
                </Layout.Sider>
                <Layout.Content style={{ padding: '24px' }}>
                    <SIPModule
                        ref={(elem) => (sipModule = elem)}
                        name={props.name}
                        sipDomain={props.sipDomain}
                        authUser={props.authUser}
                        authPass={props.authPass}
                        wssPort={props.wssPort}
                        dialerVisible={dialerVisible}
                        onClose={onDialerClose}
                        onDialerAccountClose={onDialerAccountClose}
                        dialerAccountVisible={dialerAccountVisible}
                        queues={props.queues}
                        setConnected={setConnected}
                        setCallHangup={setCallHangup}
                        {...campaignPropsForSip}
                        {...incomingCallProps}
                        {...outgoingCallProps}
                        setNumber={setNumber}
                        settings={props.settings}
                        user={props.user}
                        callId={callState.callId}
                        time={timer}
                        break={breakTimer}
                        onBreak={onBreak}
                        queueLoggedIn={queueLoggedIn}
                        isCallAccepted={isCallAccepted}
                        setIsCallAccepted={setIsCallAccepted}
                        setOutgoingDialed={setOutgoingDialed}
                        setFormVisible={setFormVisible}
                        hangupEnable={props.hangupEnable}
                    //   formWidgetDataOnCallAcceptance={formWidgetDataOnCallAcceptance}
                    //   setFormWidgetDataOnCallAcceptance={setFormWidgetDataOnCallAcceptance}
                    />
                    <CampaignWidget {...campaignProps} />
                    <FormWidget {...formProps} />
                    <WorkcodeWidget {...workcodeProps} />
                    <AgentStatusWidget
                        isLogin={queueLoggedIn}
                        isReady={isReadyQuery.data}
                        reason={aStatsQuery.data?.Pausedreason}
                    />
                    {/*<Workcode submitWorkcode={submitWorkcode} channelId={channelId} setCallHangup={setCallHangup} callHangup={callHangup} data={workcodeQuery.data} isLoading={workcodeMutation.isLoading} />*/}
                    <NotReady
                        setNotReadyReason={setNotReadyReason}
                        onOk={submitNotReady}
                        isLoading={isLoading}
                        onCancel={() => setNotReadyVisible(false)}
                        visible={notReadyVisible}
                        data={pauseReasonQuery.data}
                    //   readyButtonDisable={readyButtonDisable}
                    //   setReadyButtonDisable={setReadyButtonDisable}
                    />
                    <DialerAccount
                        onClose={onDialerAccountClose}
                        visible={dialerAccountVisible}
                        {...settingsProps}
                    />

                    {/* cedar */}
                    <CedarComponent
                        open={cedarVisible}
                        title="Student`s Data"
                        onCancel={() => setCedarVisible(false)}
                        data={cedarFetchResult}
                        loading={fetchLoading}
                        number={number}
                    />
                    <Row>
                        <Col span={8}>
                            <Spin spinning={scriptState.isLoading}>
                                <WidgetScript
                                    reload={() => dispatch(fetchAgentScript(queueState.queues))}
                                    data={scriptState.data[0]}
                                    title="Script"
                                />
                            </Spin>
                        </Col>

                        {queueStats && (
                            <Col span={8}>
                                <Spin spinning={qStatsQuery.isLoading}>
                                    <Widget
                                        reload={refreshQStats}
                                        data={qStatsQuery.data}
                                        title="Queue-stats"
                                    />
                                </Spin>
                            </Col>
                        )}
                        {agentStats && (
                            <Col span={8}>
                                <Spin spinning={aStatsQuery.isLoading}>
                                    <Widget
                                        reload={refreshAStats}
                                        data={aStatsQuery.data}
                                        title="Agent-stats"
                                    />
                                </Spin>
                            </Col>
                        )}
                        {cdrStats && (
                            <Col span={24}>
                                <CallDetailWidget
                                    loading={aCDRQuery.isLoading}
                                    data={aCDRQuery.data}
                                    setCdrPage={setCdrPage}
                                    setFilteredNumber={setFilteredNumber}
                                    filteredNumber={filteredNumber}
                                />
                            </Col>
                        )}
                        {lastCallStats && (
                            <Col span={24}>
                                <Last5CallHistory loading={false} data={callHistory} />
                            </Col>
                        )}
                    </Row>

                    <ChangePasswordModal
                        isModalOpen={passwordModal}
                        handleOk={onChangePassword}
                        form={form}
                        onCancel={() => {
                            setPasswordModal(!passwordModal)
                            form.resetFields()
                        }}
                    />
                </Layout.Content>
            </Layout>
            <Layout.Footer
                style={{ background: '#fff', textAlign: 'center', fontWeight: 'bold', color: '#15347c' }}
            >
                ContactPlus ©{new Date().getFullYear()} | Powered by Telecard Ltd.
            </Layout.Footer>
        </Layout>
    );
}


const ChangePasswordModal = ({ isModalOpen, handleOk, onCancel, form }) => {
    return (
        <Modal
            title="Change Password"
            visible={isModalOpen}
            onOk={() => {
                form.validateFields().then(values => {
                    handleOk(values)
                })
            }}
            onCancel={onCancel}
        >
            <Form
                size="large"
                form={form}
            >
                <Form.Item
                    name="current_password"
                    rules={[
                        {
                            required: true,
                            message: 'Please input your password!',
                        },
                    ]}
                >
                    <Input.Password placeholder="Enter Current Password." />
                </Form.Item>

                <Form.Item
                    name="new_password"
                    rules={[
                        {
                            required: true,
                            message: 'Please input your password!',
                        },
                    ]}
                    hasFeedback
                >
                    <Input.Password placeholder="Enter New Password." />
                </Form.Item>

                <Form.Item
                    name="new_confirm_password"
                    dependencies={['new_password']}
                    hasFeedback
                    rules={[
                        {
                            required: true,
                            message: 'Please confirm your password!',
                        },
                        ({ getFieldValue }) => ({
                            validator(_, value) {
                                if (!value || getFieldValue('new_password') === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error('The two passwords that you entered do not match!'));
                            },
                        }),
                    ]}
                >
                    <Input.Password placeholder="Enter Confirm Password." />
                </Form.Item>
            </Form>
        </Modal>
    )
}