import "./App.less";
import Main from "./pages/Main";
import { <PERSON>rows<PERSON><PERSON>outer as Router } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "react-query";
import { ReactQueryDevtools } from "react-query/devtools";
import { Provider } from "react-redux";
import { ConfigureStore } from "./App/store";

const queryClient = new QueryClient();
const store = ConfigureStore();

function App() {
    return (
        <QueryClientProvider client={queryClient}>
            <Provider store={store}>
                <Router basename="/user">
                    <Main />
                </Router>
            </Provider>
            <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
    );
}

export default App;
