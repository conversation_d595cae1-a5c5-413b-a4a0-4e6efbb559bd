import * as ActionTypes from "../Constants/QueueConstant"
import apiClient from "../config/apiClient";
import {handleError} from "../Shared/handleError";

export const getQueues = () => dispatch => {
    dispatch(queueLoading())
    apiClient.get(`/api/agent/get-queue`).then(r => dispatch(queueSuccess(r.data))).catch(e => dispatch(queueFailed(handleError(e))))
}

const queueLoading = () => ({
    type: ActionTypes.QUEUE_LOADING
})

const queueSuccess = queues => ({
    type: ActionTypes.QUEUE_SUCCESS,
    payload: queues
})

const queueFailed = err => ({
    type: ActionTypes.QUEUE_FAILED,
    payload: err
})