import * as ActionTypes from "../Constants/WorkcodeConstant"
import apiClient from "../config/apiClient";
import {handleError} from "../Shared/handleError";

export const getWorkcodes = () => dispatch => {
    dispatch(workcodeLoading())
    apiClient.get(`/api/workCode`).then(r => dispatch(workcodesSuccess(r.data))).catch(e => dispatch(workcodeFailed(handleError(e))))
}

export const postWorkcode = data => dispatch => {
    dispatch(workcodeLoading())
    apiClient.post(`/api/agent/workcode`, data).then(r => dispatch(workcodeSuccess(r.data))).catch(e => dispatch(workcodeFailed(handleError(e))))
}

const workcodesSuccess = workcodes => ({
    type: ActionTypes.WORKCODES_SUCCESS,
    payload: workcodes
})

const workcodeLoading = () => ({
    type: ActionTypes.WORKCODE_LOADING
})

const workcodeSuccess = message => ({
    type: ActionTypes.WORKCODE_SUCCESS,
    payload: message
})

const workcodeFailed = err => ({
    type: ActionTypes.WORKCODE_FAILED,
    payload: err
})