import * as ActionTypes from "../Constants/CallConstant"
import apiClient from "../config/apiClient";
import {handleError} from "../Shared/handleError";

export const getCallId = () => dispatch => {
    dispatch(callLoading())
    apiClient.post(`/api/agent/channel`).then(r => dispatch(callSuccess(r.data))).catch(e => dispatch(callFailed(handleError(e))))
}

const callLoading = () => ({
    type: ActionTypes.CALL_ID_LOADING
})

const callSuccess = callId => ({
    type: ActionTypes.CALL_ID_SUCCESS,
    payload: callId
})

const callFailed = err => ({
    type: ActionTypes.CALL_ID_FAILED,
    payload: err
})