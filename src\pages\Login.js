import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Checkbox, Col, Form, Input, notification, Row, Spin } from "antd";
import { LockOutlined, UserOutlined } from "@ant-design/icons";
import apiClient from "../config/apiClient";
import { login } from "../config/routes"
import { useState, useEffect } from "react"
import { useHistory } from "react-router-dom";
import logo from '../logo-lg.png'

import '../App.css'

export default function Login(props) {

    const openNotificationWithIcon = (message) => {
        notification['error']({
            message: 'Error',
            description: message,
        });
    }

    const [loading, setLoading] = useState(false)
    const [error, setError] = useState(false)

    let history = useHistory()

    // const onFinish = (values) => {
    //     setLoading(true)
    //     apiClient.post(login, {
    //         username: values.username,
    //         password: values.password
    //     }).then(response => {
    //         setLoading(false)
    //         sessionStorage.setItem('token', response?.data?.token)
    //         sessionStorage.setItem('id', response?.data?.id)
    //         sessionStorage.setItem('agent_username',response?.data?.username)
    //         sessionStorage.setItem('auto_call_answer',response?.data?.auto_call_answer)
    //         if (response.status === 200) {
    //             props.setUsername(sessionStorage.setItem('agent_username',response?.data?.username))
    //             sessionStorage.setItem('loggedin', "true")
    //             props.setLoggedin(true)
    //         } else if (response.status === 302) {
    //             // Already logged in
    //             history.push('/dialer')
    //             props.setLoggedin(true)
    //         }
    //     }).catch(error => {
    //         if (error.response) {
    //             console.log(error.response)
    //             setError(error.response.data.message)
    //             setLoading(false)
    //         } else {
    //             console.log(error.message)
    //             setLoading(false)
    //             setError(error.message)
    //         }
    //     })

    // }

    const onFinish = async (values) => {
        try {
            setLoading(true);
            const response = await apiClient.post(login, {
                username: values.username,
                password: values.password,
            });

            setLoading(false);

            sessionStorage.setItem("agent_token", response?.data?.token);
            sessionStorage.setItem("id", response?.data?.id);
            sessionStorage.setItem("agent_username", response?.data?.username);
            sessionStorage.setItem("auto_call_answer", response?.data?.auto_call_answer);

            if (response.status === 200) {
                await props.setUsername(sessionStorage.getItem("agent_username"));
                sessionStorage.setItem("loggedin", "true");
                props.setLoggedin(true);
                history.push('/dialer')
            } else if (response.status === 302) {
                // Already logged in
                history.push("/dialer");
                props.setLoggedin(true);
            }
        } catch (error) {
            if (error.response) {
                console.log(error.response);
                setError(error.response.data.message);
                setLoading(false);
            } else {
                console.log(error.message);
                setLoading(false);
                setError(error.message);
            }
        }
    };

    useEffect(() => {
        if (error) {
            openNotificationWithIcon(error)
        }
    }, [error])

    const onFinishFailed = (errorInfo) => {
        console.log('Failed:', errorInfo);
    }
    const style = {
        height: '40px',
        width: '100%',
        // borderRadius: '20px',
    }

    return (
        <Spin spinning={loading}>
            <div className="bg-image" style={{}}>
                <img src={logo} alt="" height={80} width={250} style={{ position: 'absolute', top: "10px", left: '20px', padding: 0 }} />
                <Card style={{ padding: '10px', background: 'none', border: 'none', marginLeft: '25px', width: '500px' }}>
                    <div style={{ textAlign: 'center', marginBottom: '15px' }}>
                        {error && <Alert
                            message="Error"
                            description={error}
                            type="error"
                            showIcon
                            style={{ marginBottom: 20 }}
                        />}
                    </div>
                    <div className="top-heading" >

                        Agent Login
                    </div>
                    <Form
                        // ref={formRef}
                        onFinish={onFinish}
                        onFinishFailed={onFinishFailed}
                        // initialValues={record}
                        size="large"
                        action="#"
                        id="sign-in"
                    >
                        <div style={{ marginBottom: 0 }}>
                            <Form.Item
                                name="username"
                                rules={[
                                    {
                                        required: true,
                                        message: "Please input your username!",
                                    },
                                ]}

                            >
                                <Input
                                    prefix={<UserOutlined className="site-form-item-icon outlined-input" />}
                                    placeholder="Username"
                                    style={style}
                                />
                            </Form.Item>

                            <Form.Item
                                name="password"
                                rules={[
                                    {
                                        required: true,
                                        message: "Please input your password!",
                                    },
                                ]}
                            >
                                <Input.Password
                                    style={style}
                                    prefix={<LockOutlined className="site-form-item-icon" />}
                                    placeholder="Password"
                                />
                            </Form.Item>
                            <Form.Item name="remember" valuePropName="checked" >
                                <Checkbox ><b style={{ color: '#15347c' }}>Remember me</b></Checkbox>
                            </Form.Item>
                        </div>

                        <div >
                            <Form.Item>
                                <Button
                                    size="large"
                                    type="primary"
                                    style={{ height: '40px', background: '#15347c' }}
                                    block
                                    htmlType="submit"
                                >
                                    Login
                                </Button>
                            </Form.Item>
                        </div>
                    </Form>

                </Card>
            </div>
        </Spin>
    )
}