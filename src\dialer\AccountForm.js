import { Button, Form, Input, InputNumber, Switch } from 'antd'
import { useEffect, useState } from "react"

const AccountForm = props => {

    const [form] = Form.useForm()
    const [cdrStatus, setCdrStatus] = useState(false)
    const [lastCalls, setLastCalls] = useState(false)

    const onFinish = (values) => {
        console.log(values)
    }

    return (
        <Form form={form} name="nest-messages" onFinish={onFinish} initialValues={{ queueStats: props.queueStats, agentStats: props.agentStats, enableCdr: props.cdrStats, enableAa: props.aaStats }}>
            <Form.Item name="queueStats" label="Enable Q-Stats" valuePropName="checked">
                <Switch onChange={() => props.setQueueStats(!props.queueStats)} />
            </Form.Item>
            <Form.Item name="agentStats" label="Enable Agent-Stats" valuePropName="checked">
                <Switch onChange={() => props.setAgentStats(!props.agentStats)} />
            </Form.Item>
            <Form.Item name="enableCdr" label="Enable CDRs" valuePropName="radio">
                <Switch checked={cdrStatus} onChange={(v) => {
                    setLastCalls(!v)
                    setCdrStatus(v)
                    props.setCdrStats(v)
                    props.setLastCallStats(!v)
                    // props.setCdrStats(!props.cdrStats)
                }} />
            </Form.Item>
            <Form.Item name="enableLastCalls" label="Last 5 calls" valuePropName="radio">
                <Switch checked={lastCalls} onChange={(v) => {
                    setCdrStatus(!v)
                    setLastCalls(v)
                    props.setLastCallStats(v)
                    props.setCdrStats(!v)
                    // props.setLastCallStats(!props.lastCallStats)
                }} />
            </Form.Item>
            <Form.Item name="refreshInterval" label="Refresh Interval (seconds)">
                <InputNumber defaultValue={props.refetchInterval} onChange={value => props.setRefetchInterval(value)} min={1} max={99} />
            </Form.Item>
        </Form>
    );
}

export default AccountForm