
import {Card, Table} from "antd";

const Last5CallHistory = ({ data, loading }) => {

    const columns = [
        {
            title: 'Accountcode',
            dataIndex: 'accountcode',
            key: 'accountcode'
        },
        {
            title: 'Source',
            dataIndex: 'src',
            key: 'src',
        },
        {
            title: 'Destination',
            dataIndex: 'dst',
            key: 'dst',
        },
        {
            title: 'Agent Name',
            dataIndex: 'agent',
            key: 'agent',
            width: 150,
            render: (v)=>`${v ? v : 'N/A'} `,
        },
        {
            title: 'Channel',
            dataIndex: 'channel',
            key: 'channel',
        },
        {
            title: 'Start',
            dataIndex: 'start',
            key: 'start',
        },
        {
            title: 'Answer',
            dataIndex: 'answer',
            key: 'answer',
        },
        {
            title: 'End',
            dataIndex: 'end',
            key: 'end',
        },
        {
            title: 'Duration',
            dataIndex: 'billsec',
            key: 'billsec',
        },
        {
            title: 'Disposition',
            dataIndex: 'disposition',
            key: 'disposition',
        },
    ]

    return(
        <Card>
            <Table loading={loading} scroll={{x: true}} dataSource={data} columns={columns} />
        </Card>
    )
}

export default Last5CallHistory