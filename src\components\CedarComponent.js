import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Divider, Form, Input, Modal, Row, Space, Spin, Typography } from 'antd'
import React, { useEffect } from 'react'

function CedarComponent({ open, title, onCancel, data, loading, number }) {
    const [form] = Form.useForm();

    useEffect(() => {
        if (data) form.setFieldsValue(data[0])
    }, [data])

    // console.log("form data", data);
    return (
        <Modal
            title={(
                <Space>
                    <Typography.Title level={4} style={{ margin: 0 }}>{title}</Typography.Title>
                    <Button type="primary" href={`${process.env.REACT_APP_TICKET_URL}?student_mobile_no=${number}`} target="_blank">Create Ticket</Button>
                </Space>
            )}
            open={open}
            onCancel={() => {
                form.resetFields();
                onCancel();
            }}
            width={800}
            onOk={() => {
                form.resetFields();
                onCancel();
            }}
        >
            <Spin spinning={loading}>
                {data.length > 0 ? data.map((d, i) => (
                    <div key={i}>
                        {data.length > 1 && <Divider />}
                        <Form form={form} key={i} size='large' >
                            <Row gutter={[16, 0]}>
                                {Object.keys(d).map((key) => <Col span={12}>
                                    <Form.Item
                                        style={{ textTransform: 'capitalize' }}
                                        key={key}
                                        name={key}
                                        // value={d.key}
                                        label={key.split('_').join(" ")}
                                        labelCol={{ span: 24 }}
                                        wrapperCol={{ span: 24 }}
                                    >
                                        <Input />
                                    </Form.Item>
                                </Col>)}
                            </Row>
                        </Form>
                    </div>
                )) : <Alert type='info' message="no result found" />}
            </Spin>
        </Modal>
    )
}

export default CedarComponent