{"name": "visual-phone", "version": "0.1.0", "private": true, "homepage": "./", "dependencies": {"@ant-design/icons": "^4.4.0", "@craco/craco": "^6.0.0", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "antd": "^4.10.3", "axios": "^0.21.4", "craco-less": "^1.17.1", "laravel-echo": "^1.11.7", "moment-timezone": "^0.5.43", "react": "^17.0.1", "react-audio-player": "^0.17.0", "react-compound-timer": "^1.2.0", "react-desktop-notification": "^1.0.9", "react-dom": "^17.0.1", "react-highlight-words": "^0.17.0", "react-query": "^3.5.16", "react-redux": "^7.2.4", "react-router-dom": "^5.2.0", "react-scripts": "4.0.1", "react-storage-hooks": "^4.0.1", "redux": "^4.1.0", "redux-logger": "^3.0.6", "redux-thunk": "^2.3.0", "sip.js": "^0.18.1", "web-vitals": "^0.2.4"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "craco eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"pusher-js": "^7.1.1-beta"}}