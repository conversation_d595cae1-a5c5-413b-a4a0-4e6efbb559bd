import * as ActionTypes from "../Constants/WorkcodeConstant"

const initialState = {
    workcodes: [],
    message: false,
    errMess: false,
    isLoading: false
}

export const WorkcodeReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.WORKCODES_SUCCESS:
            return {...state, isLoading: false, workcodes: action.payload}
        case ActionTypes.WORKCODE_LOADING:
            return {...state, isLoading: true}
        case ActionTypes.WORKCODE_SUCCESS:
            return {...state, message: action.payload, errMess: false, isLoading: false}
        case ActionTypes.WORKCODE_FAILED:
            return {...state, errMess: action.payload, message: false, isLoading: false}
    }

}