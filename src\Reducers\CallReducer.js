import * as ActionTypes from "../Constants/CallConstant"

const initialState = {
    callId: "",
    errMess: "",
    isLoading: false
}

export const CallReducer = (state = initialState, action) => {
    switch (action.type) {
        default:
            return state
        case ActionTypes.CALL_ID_LOADING:
            return { ...state, isLoading: true }
        case ActionTypes.CALL_ID_SUCCESS:
            return { ...state, isLoading: false, callId: action.payload }
        case ActionTypes.CALL_ID_FAILED:
            return { ...state, isLoading: false, errMess: action.payload }
    }
}