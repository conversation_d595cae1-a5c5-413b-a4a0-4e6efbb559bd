import apiClient from "./apiClient";
import { notReadyAgent, submitWorkcode, readyAgent } from "./routes";

export const postWorkcode = data => apiClient.post(submitWorkcode, data)
export const postNotReady = async reason => {
    let result = apiClient.post(notReadyAgent, reason)
    // sessionStorage.setItem('break_time',r.data[1]
    result.then((r) => {
        if (r.data[1]?.length > 0) {
            sessionStorage.setItem('break_time', r.data[1])
        }
    })
    return result
}
export const postUnPause = async reason => {
    let result = await apiClient.post(readyAgent, reason)
    // result.data && sessionStorage.removeItem('break_time')
    return result.data
}